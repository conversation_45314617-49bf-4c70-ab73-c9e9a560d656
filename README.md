# 📊 SharePoint BOM Processor

Un script Python pour automatiser le traitement et la fusion de fichiers BOM (Bill of Materials) sur SharePoint.

## 🎯 Fonctionnalités

- ✅ **Authentification SharePoint** avec gestion MFA
- 📥 **Téléchargement automatique** du fichier master depuis SharePoint
- 📊 **Fusion intelligente** des données BOM
- 🔍 **Détection automatique** des colonnes de référence
- 📤 **Upload automatique** du fichier modifié vers SharePoint
- 🛡️ **Gestion d'erreurs** complète avec messages détaillés

## 🚀 Installation

### Prérequis
- Python 3.7+
- Accès à un site SharePoint
- Permissions de lecture/écriture sur le dossier SharePoint cible

### Dépendances
```bash
pip install Office365-REST-Python-Client pandas msal
```

## ⚙️ Configuration

### 1. Paramètres de base
Modifiez les variables dans `traitement_bom_sharepoint.py` :

```python
# Configuration SharePoint
site_url = "https://votre-organisation.sharepoint.com/sites/VotreSite"
username = "<EMAIL>"
password = "votre_mot_de_passe"

# Chemins des fichiers
sharepoint_folder = "/sites/VotreSite/Shared Documents"
master_file_name = "MasterBOM.xlsx"
modified_file_name = "master_bom_modifie.xlsx"
local_new_bom = "nouvelle_bom.xlsx"
```

### 2. Structure des fichiers Excel
Le script recherche automatiquement une colonne de référence parmi :
- `Réf`
- `Ref`
- `Reference`
- `Part Number`
- `PN`

## 🔧 Utilisation

### Exécution simple
```bash
python traitement_bom_sharepoint.py
```

### Processus automatique
1. **Authentification** - Se connecte à SharePoint (peut ouvrir le navigateur pour MFA)
2. **Vérification** - Liste les fichiers disponibles dans le dossier
3. **Téléchargement** - Récupère le fichier master depuis SharePoint
4. **Traitement** - Fusionne les nouvelles références avec le master
5. **Upload** - Remet le fichier modifié sur SharePoint

## 📋 Exemple de sortie

```
🔄 Début du traitement BOM SharePoint...
🔐 Authentification SharePoint avec MSAL (va ouvrir le navigateur)...
✅ Authentification réussie! Site: Mon Site SharePoint
📁 Vérification d'accès au dossier...
✅ Accès au dossier réussi: /sites/MonSite/Shared Documents
📋 Liste des fichiers dans le dossier...
📁 Fichiers trouvés (5 fichiers):
  - MasterBOM.xlsx
  - Document1.docx
  - ...
📥 Téléchargement du fichier 'MasterBOM.xlsx'...
✅ Fichier 'MasterBOM.xlsx' téléchargé avec succès.
📊 Traitement des fichiers Excel...
📋 Master BOM: 150 lignes
📋 Nouvelle BOM: 25 lignes
✅ Colonne de référence utilisée: 'Réf'
📝 Nouvelles références trouvées: 8
✅ Fichier modifié sauvegardé: 'master_bom_modifie.xlsx' (158 lignes)
📤 Upload du fichier modifié vers SharePoint...
✅ Upload terminé avec succès!
📁 Fichier disponible sur SharePoint: master_bom_modifie.xlsx

🏁 Traitement terminé!
```

## 🔐 Authentification

Le script supporte plusieurs méthodes d'authentification :

### Méthode 1 : Authentification basique
```python
# Avec email et mot de passe
username = "<EMAIL>"
password = "votre_mot_de_passe"
```

### Méthode 2 : MSAL (Recommandée)
- Gère automatiquement MFA
- Ouvre le navigateur pour l'authentification
- Plus sécurisé pour les environnements d'entreprise

## 📁 Structure du projet

```
sharepoint-bom-processor/
├── traitement_bom_sharepoint.py    # Script principal
├── README.md                       # Documentation
├── requirements.txt                # Dépendances
├── nouvelle_bom.xlsx              # Fichier d'exemple (généré automatiquement)
└── .gitignore                     # Fichiers à ignorer
```

## 🛠️ Dépannage

### Erreurs courantes

#### `AADSTS50076: Multi-factor authentication required`
**Solution :** Le script utilise automatiquement MSAL pour gérer MFA

#### `No authenticated credentials found`
**Solutions :**
1. Vérifiez votre email et mot de passe
2. Assurez-vous d'avoir accès au site SharePoint
3. Contactez votre administrateur IT

#### `Aucune colonne de référence commune trouvée`
**Solution :** Assurez-vous que vos fichiers Excel contiennent une colonne de référence (`Réf`, `Reference`, etc.)

### Logs détaillés
Le script affiche des messages détaillés à chaque étape pour faciliter le diagnostic.

## 🔒 Sécurité

⚠️ **Important :** Ne commitez jamais vos identifiants dans le code !

### Bonnes pratiques :
1. Utilisez des variables d'environnement
2. Créez un fichier `config.py` (ajouté au `.gitignore`)
3. Utilisez l'authentification MSAL quand possible

## 📝 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🤝 Contribution

Les contributions sont les bienvenues ! N'hésitez pas à :
1. Fork le projet
2. Créer une branche pour votre fonctionnalité
3. Commiter vos changements
4. Pousser vers la branche
5. Ouvrir une Pull Request

## 📞 Support

Pour toute question ou problème :
- Ouvrez une issue sur GitHub
- Consultez la section Dépannage ci-dessus
- Vérifiez les logs détaillés du script

---

**Développé avec ❤️ pour automatiser le traitement des BOM SharePoint**
