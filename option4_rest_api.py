import requests
import json
from requests.auth import HTTPBasicAuth

# ---- 🔐 OPTION 4: API REST SharePoint directe ----
# Utilise l'API REST de SharePoint avec authentification basique

site_url = "https://uitacma.sharepoint.com/sites/YAZAKIInternship"
username = "<EMAIL>"
password = "Sppu2378"

print("🔄 Test avec API REST SharePoint...")

try:
    # URL de l'API REST pour obtenir les informations du site
    api_url = f"{site_url}/_api/web"
    
    print("🔐 Test d'authentification REST...")
    
    # Headers pour l'API REST
    headers = {
        'Accept': 'application/json;odata=verbose',
        'Content-Type': 'application/json;odata=verbose'
    }
    
    # Authentification basique
    auth = HTTPBasicAuth(username, password)
    
    # Test de connexion
    response = requests.get(api_url, headers=headers, auth=auth)
    
    if response.status_code == 200:
        site_info = response.json()
        print(f"✅ Connexion REST réussie!")
        print(f"  - Titre: {site_info['d']['Title']}")
        print(f"  - URL: {site_info['d']['Url']}")
        
        # Test d'accès aux fichiers
        files_url = f"{site_url}/_api/web/GetFolderByServerRelativeUrl('/sites/YAZAKIInternship/Shared Documents')/Files"
        files_response = requests.get(files_url, headers=headers, auth=auth)
        
        if files_response.status_code == 200:
            files_data = files_response.json()
            files = files_data['d']['results']
            
            print(f"📁 Fichiers trouvés ({len(files)} fichiers):")
            for file in files:
                print(f"  - {file['Name']} ({file['Length']} bytes)")
        else:
            print(f"❌ Erreur d'accès aux fichiers: {files_response.status_code}")
            print(f"Réponse: {files_response.text}")
    
    else:
        print(f"❌ Erreur de connexion: {response.status_code}")
        print(f"Réponse: {response.text}")

except Exception as e:
    print(f"❌ Erreur: {e}")
    print("\n💡 Cette méthode utilise l'API REST directe")
    print("- Peut contourner certains problèmes d'authentification")
    print("- Mais peut aussi être bloquée par MFA")

print("\n🏁 Test terminé!")
