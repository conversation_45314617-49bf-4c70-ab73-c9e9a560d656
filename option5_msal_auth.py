import os
from office365.sharepoint.client_context import ClientContext

# ---- 🔐 OPTION 5: Authentification moderne avec MSAL ----
# Utilise Microsoft Authentication Library pour gérer MFA

site_url = "https://uitacma.sharepoint.com/sites/YAZAKIInternship"
username = "<EMAIL>"

# Client ID de l'application SharePoint Online (ID public Microsoft)
client_id = "9bc3ab49-b65d-410a-85ad-de819febfddc"  # SharePoint Online Client Extensibility Web Application Principal

sharepoint_folder = "/sites/YAZAKIInternship/Shared Documents"
test_file_name = "MasterBOM Test.xlsx"

print("🔄 Test avec authentification MSAL moderne...")

try:
    print("🔐 Authentification MSAL (peut ouvrir le navigateur)...")
    
    # Méthode avec authentification moderne
    def acquire_token():
        """Fonction pour acquérir un token avec MSAL"""
        try:
            import msal
            
            # Configuration MSAL
            authority = "https://login.microsoftonline.com/common"
            scopes = [f"{site_url}/.default"]
            
            # Application publique MSAL
            app = msal.PublicClientApplication(
                client_id=client_id,
                authority=authority
            )
            
            # Tentative d'authentification silencieuse d'abord
            accounts = app.get_accounts(username=username)
            if accounts:
                result = app.acquire_token_silent(scopes, account=accounts[0])
                if result:
                    return result["access_token"]
            
            # Authentification interactive si nécessaire
            result = app.acquire_token_interactive(
                scopes=scopes,
                login_hint=username
            )
            
            if "access_token" in result:
                return result["access_token"]
            else:
                raise Exception(f"Erreur d'authentification: {result.get('error_description', 'Inconnue')}")
                
        except ImportError:
            raise Exception("La bibliothèque 'msal' n'est pas installée. Installez-la avec: pip install msal")
    
    # Acquérir le token
    access_token = acquire_token()
    print("✅ Token d'accès acquis!")
    
    # Créer le contexte avec le token
    ctx = ClientContext(site_url).with_access_token(access_token)
    
    # Test de connexion
    web = ctx.web
    ctx.load(web)
    ctx.execute_query()
    
    print(f"✅ Connexion réussie! Site: {web.title}")
    
    # Test d'accès au dossier
    folder = ctx.web.get_folder_by_server_relative_url(sharepoint_folder)
    ctx.load(folder)
    ctx.execute_query()
    print(f"✅ Accès au dossier réussi: {sharepoint_folder}")
    
    # Lister les fichiers
    files = folder.files
    ctx.load(files)
    ctx.execute_query()
    
    print(f"📁 Fichiers trouvés ({len(files)} fichiers):")
    for file in files:
        print(f"  - {file.name}")

except Exception as e:
    print(f"❌ Erreur: {e}")
    print("\n💡 Pour utiliser cette méthode:")
    print("1. Installez MSAL: pip install msal")
    print("2. Cette méthode gère automatiquement MFA")
    print("3. Peut ouvrir le navigateur pour l'authentification")

print("\n🏁 Test terminé!")
