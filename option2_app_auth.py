import os
from office365.sharepoint.client_context import Client<PERSON>ontext
from office365.runtime.auth.client_credential import ClientCredential

# ---- 🔐 OPTION 2: Authentification via App Registration ----
# Cette méthode contourne le MFA en utilisant une application enregistrée

# 🛠️ Paramètres à configurer dans Azure AD
site_url = "https://uitacma.sharepoint.com/sites/YAZAKIInternship"
client_id = "VOTRE_CLIENT_ID"  # 🛠️ À obtenir depuis Azure AD
client_secret = "VOTRE_CLIENT_SECRET"  # 🛠️ À obtenir depuis Azure AD

sharepoint_folder = "/sites/YAZAKIInternship/Shared Documents"
test_file_name = "MasterBOM Test.xlsx"

print("🔄 Test avec App Registration...")

try:
    # Authentification via Client Credentials
    print("🔐 Authentification via App Registration...")
    credentials = ClientCredential(client_id, client_secret)
    ctx = ClientContext(site_url).with_credentials(credentials)
    
    # Test de connexion
    web = ctx.web
    ctx.load(web)
    ctx.execute_query()
    
    print(f"✅ Connexion réussie! Site: {web.title}")
    
    # Test d'accès au dossier
    folder = ctx.web.get_folder_by_server_relative_url(sharepoint_folder)
    ctx.load(folder)
    ctx.execute_query()
    print(f"✅ Accès au dossier réussi: {sharepoint_folder}")
    
    # Lister les fichiers
    files = folder.files
    ctx.load(files)
    ctx.execute_query()
    
    print(f"📁 Fichiers trouvés ({len(files)} fichiers):")
    for file in files:
        print(f"  - {file.name}")

except Exception as e:
    print(f"❌ Erreur: {e}")
    print("\n💡 Pour configurer l'App Registration:")
    print("1. Allez sur https://portal.azure.com")
    print("2. Azure Active Directory > App registrations > New registration")
    print("3. Configurez les permissions SharePoint")
    print("4. Générez un client secret")

print("\n🏁 Test terminé!")
