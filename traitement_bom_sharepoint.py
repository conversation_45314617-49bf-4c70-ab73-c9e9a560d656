import os
import pandas as pd
from office365.sharepoint.client_context import ClientContext
from office365.runtime.auth.authentication_context import AuthenticationContext

# ---- 🔐 1. Paramètres à personnaliser ----
site_url = "https://emsiedu.sharepoint.com/sites/Yazakiintership"  # 🛠️ lien du site (PAS le lien de partage)
username = "<EMAIL>"
password = "Sppu2378"

# Chemin relatif du dossier où sont stockés les fichiers dans SharePoint
sharepoint_folder = "/sites/Yazakiintership/Shared Documents/BOM"  # 🛠️ à adapter selon ton SharePoint

master_file_name = "Master Ford Ilyass (2).xlsx"
modified_file_name = "master_bom_modifie.xlsx"
local_new_bom = "nouvelle_bom.xlsx"  # Fichier Excel à fusionner, doit exister localement

# ---- 🔐 2. Authentification ----
ctx_auth = AuthenticationContext(site_url)
if not ctx_auth.acquire_token_for_user(username, password):
    raise Exception("❌ Authentification échouée. Vérifie les identifiants et MFA.")

ctx = ClientContext(site_url, ctx_auth)

# ---- 📥 3. Télécharger le master depuis SharePoint ----
master_file_url = f"{sharepoint_folder}/{master_file_name}"

with open(master_file_name, "wb") as local_file:
    file = ctx.web.get_file_by_server_relative_url(master_file_url)
    file.download(local_file).execute_query()

print(f"✅ Fichier '{master_file_name}' téléchargé.")
ctx.execute_query()

with open(master_file_name, "wb") as local_file:
    local_file.write(response.content)
print(f"✅ Fichier '{master_file_name}' téléchargé.")

# ---- 📊 4. Traitement Excel ----
master_df = pd.read_excel(master_file_name)
new_df = pd.read_excel(local_new_bom)

# Filtrer les nouvelles références
if "Réf" not in master_df.columns or "Réf" not in new_df.columns:
    raise Exception("❌ La colonne 'Réf' est manquante dans un des fichiers Excel.")

new_refs = new_df[~new_df["Réf"].isin(master_df["Réf"])]
result_df = pd.concat([master_df, new_refs], ignore_index=True)

# Sauvegarde locale du fichier modifié
result_df.to_excel(modified_file_name, index=False)
print(f"✅ Nouveau fichier enregistré sous '{modified_file_name}'.")

# ---- 📤 5. Upload vers SharePoint ----
with open(modified_file_name, "rb") as content_file:
    content = content_file.read()

target_folder = ctx.web.get_folder_by_server_relative_url(sharepoint_folder)
target_folder.upload_file(modified_file_name, content)
ctx.execute_query()

print("✅ Upload terminé avec succès.")
