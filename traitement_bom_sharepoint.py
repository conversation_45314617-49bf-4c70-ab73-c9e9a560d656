import os
import pandas as pd
from datetime import datetime
from office365.sharepoint.client_context import ClientContext

# ---- 🔐 1. Paramètres à personnaliser ----
site_url = "https://uitacma.sharepoint.com/sites/YAZAKIInternship"  # 🛠️ URL du site
username = "EMAIL"  # 🛠️ Votre email
password = "VOTRE_MOT_DE_PASSE"  # 🛠️ Ajoutez votre mot de passe ici


# Chemins des fichiers
sharepoint_folder = "/sites/YAZAKIInternship/Shared Documents"  # 🛠️ Dossier SharePoint
master_file_name = "MasterBOM Test.xlsx"  # 🛠️ Fichier master sur SharePoint
backup_file_name = f"MasterBOM_Backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"  # 🛡️ Sauvegarde automatique
modified_file_name = "MasterBOM Test.xlsx"  # 🛠️ ÉCRASE L'ORIGINAL (avec sauvegarde)
local_new_bom = "nouvelle_bom.xlsx"  # 🛠️ Fichier local à fusionner (doit exister)

print("🔄 Début du traitement BOM SharePoint...")

def authenticate_sharepoint():
    """Authentification SharePoint - teste plusieurs méthodes"""

    # ---- 🔧 Méthode 1: Authentification basique (email + mot de passe) ----
    try:
        print("🔐 Test 1: Authentification basique (email + mot de passe)...")

        from office365.runtime.auth.authentication_context import AuthenticationContext

        ctx_auth = AuthenticationContext(site_url)
        if ctx_auth.acquire_token_for_user(username, password):
            ctx = ClientContext(site_url, ctx_auth)

            # Test de connexion
            web = ctx.web
            ctx.load(web)
            ctx.execute_query()

            print(f"✅ Authentification basique réussie! Site: {web.title}")
            return ctx
        else:
            print("❌ Authentification basique échouée")

    except Exception as e:
        print(f"❌ Erreur authentification basique: {e}")

    # ---- 🔧 Méthode 2: Authentification MSAL ----
    try:
        print("🔐 Test 2: Authentification MSAL (va ouvrir le navigateur)...")

        import msal

        # Configuration MSAL
        client_id = "9bc3ab49-b65d-410a-85ad-de819febfddc"  # SharePoint Online Client Extensibility
        authority = "https://login.microsoftonline.com/common"
        scopes = [f"{site_url}/.default"]

        # Application publique MSAL
        app = msal.PublicClientApplication(
            client_id=client_id,
            authority=authority
        )

        print("🌐 Ouverture du navigateur pour l'authentification...")

        # Authentification interactive
        result = app.acquire_token_interactive(
            scopes=scopes,
            login_hint=username
        )

        if "access_token" in result:
            print("✅ Token d'accès acquis!")

            # Créer le contexte avec le token
            ctx = ClientContext(site_url).with_access_token(result["access_token"])

            # Test de connexion
            web = ctx.web
            ctx.load(web)
            ctx.execute_query()

            print(f"✅ Authentification réussie! Site: {web.title}")
            return ctx
        else:
            error_msg = result.get('error_description', 'Erreur inconnue')
            raise Exception(f"Erreur d'authentification MSAL: {error_msg}")

    except ImportError:
        print("❌ La bibliothèque 'msal' n'est pas installée.")
        print("💡 Installez-la avec: python -m pip install msal")
        return None
    except Exception as e:
        print(f"❌ Erreur d'authentification: {e}")
        print("\n💡 Solutions alternatives:")
        print("1. Vérifiez que votre email est correct")
        print("2. Assurez-vous d'avoir accès au site SharePoint")
        print("3. Autorisez l'application dans le navigateur")
        print("4. Contactez votre administrateur IT pour les permissions")
        return None

try:
    # ---- 🔐 2. Authentification ----
    ctx = authenticate_sharepoint()
    if not ctx:
        raise Exception("Authentification échouée")
    
    # ---- 📁 3. Test d'accès au dossier ----
    print("📁 Vérification d'accès au dossier...")
    folder = ctx.web.get_folder_by_server_relative_url(sharepoint_folder)
    ctx.load(folder)
    ctx.execute_query()
    print(f"✅ Accès au dossier réussi: {sharepoint_folder}")
    
    # ---- 📋 4. Liste des fichiers ----
    print("📋 Liste des fichiers dans le dossier...")
    files = folder.files
    ctx.load(files)
    ctx.execute_query()
    
    print(f"📁 Fichiers trouvés ({len(files)} fichiers):")
    for file in files:
        print(f"  - {file.name}")
    
    # ---- 📥 5. Téléchargement du fichier master ----
    print(f"📥 Téléchargement du fichier '{master_file_name}'...")
    master_file_url = f"{sharepoint_folder}/{master_file_name}"
    
    try:
        with open(master_file_name, "wb") as local_file:
            file = ctx.web.get_file_by_server_relative_url(master_file_url)
            file.download(local_file).execute_query()
        
        print(f"✅ Fichier '{master_file_name}' téléchargé avec succès.")

        # ---- 🛡️ 6. Création de la sauvegarde du Master BOM ----
        print(f"🛡️ Création de la sauvegarde: '{backup_file_name}'...")
        try:
            # Copier le fichier master téléchargé comme sauvegarde
            import shutil
            shutil.copy2(master_file_name, backup_file_name)
            print(f"✅ Sauvegarde créée localement: '{backup_file_name}'")

            # Upload de la sauvegarde vers SharePoint
            print("📤 Upload de la sauvegarde vers SharePoint...")
            with open(backup_file_name, "rb") as backup_content:
                backup_data = backup_content.read()

            backup_folder = ctx.web.get_folder_by_server_relative_url(sharepoint_folder)
            backup_folder.upload_file(backup_file_name, backup_data)
            ctx.execute_query()

            print(f"✅ Sauvegarde uploadée sur SharePoint: '{backup_file_name}'")

        except Exception as backup_error:
            print(f"⚠️ Erreur lors de la sauvegarde: {backup_error}")
            print("⚠️ Continuons quand même, mais soyez prudent !")

        # ---- 📊 8. Vérification du fichier local à fusionner ----
        if not os.path.exists(local_new_bom):
            print(f"⚠️  Le fichier '{local_new_bom}' n'existe pas localement.")
            print("📝 Création d'un fichier exemple...")

            # Créer un fichier exemple pour la démonstration
            example_df = pd.DataFrame({
                'Réf': ['REF001', 'REF002', 'REF003'],
                'Description': ['Composant A', 'Composant B', 'Composant C'],
                'Quantité': [10, 20, 15]
            })
            example_df.to_excel(local_new_bom, index=False)
            print(f"✅ Fichier exemple '{local_new_bom}' créé.")

        # ---- 📊 9. Traitement Excel ----
        print("📊 Traitement des fichiers Excel...")
        master_df = pd.read_excel(master_file_name)
        new_df = pd.read_excel(local_new_bom)
        
        print(f"📋 Master BOM: {len(master_df)} lignes")
        print(f"📋 Nouvelle BOM: {len(new_df)} lignes")
        
        # Vérifier la colonne de référence
        ref_column = None
        for col in ['Réf', 'Ref', 'Reference', 'Part Number', 'PN']:
            if col in master_df.columns and col in new_df.columns:
                ref_column = col
                break
        
        if not ref_column:
            print("⚠️  Colonnes disponibles dans master:", list(master_df.columns))
            print("⚠️  Colonnes disponibles dans nouveau:", list(new_df.columns))
            raise Exception("❌ Aucune colonne de référence commune trouvée.")
        
        print(f"✅ Colonne de référence utilisée: '{ref_column}'")
        
        # Filtrer les nouvelles références
        new_refs = new_df[~new_df[ref_column].isin(master_df[ref_column])]
        print(f"📝 Nouvelles références trouvées: {len(new_refs)}")
        
        # Fusionner les données
        result_df = pd.concat([master_df, new_refs], ignore_index=True)
        
        # Sauvegarde locale
        result_df.to_excel(modified_file_name, index=False)
        print(f"✅ Fichier modifié sauvegardé: '{modified_file_name}' ({len(result_df)} lignes)")

        # ---- 📤 10. Upload vers SharePoint (ÉCRASE L'ORIGINAL) ----
        print("⚠️ ATTENTION: Le fichier original va être écrasé !")
        print(f"🛡️ Sauvegarde disponible: '{backup_file_name}'")

        # Demande de confirmation (optionnel - commentez ces lignes pour automatiser)
        # confirmation = input("Voulez-vous vraiment écraser le Master BOM original ? (oui/non): ")
        # if confirmation.lower() not in ['oui', 'o', 'yes', 'y']:
        #     print("❌ Opération annulée par l'utilisateur.")
        #     return

        print("📤 Upload du fichier modifié vers SharePoint (écrase l'original)...")
        with open(modified_file_name, "rb") as content_file:
            content = content_file.read()

        target_folder = ctx.web.get_folder_by_server_relative_url(sharepoint_folder)
        target_folder.upload_file(modified_file_name, content)
        ctx.execute_query()

        print("✅ Upload terminé avec succès!")
        print(f"🔄 Fichier original écrasé: {modified_file_name}")
        print(f"🛡️ Sauvegarde disponible: {backup_file_name}")
        print("📊 Résumé:")
        print(f"  - Anciennes références: {len(master_df)}")
        print(f"  - Nouvelles références ajoutées: {len(new_refs)}")
        print(f"  - Total final: {len(result_df)}")
        
    except Exception as file_error:
        print(f"❌ Erreur avec le fichier '{master_file_name}': {file_error}")
        print("💡 Vérifiez que le nom du fichier est correct dans SharePoint.")

except Exception as main_error:
    print(f"❌ Erreur principale: {main_error}")
    print("\n💡 Solutions possibles:")
    print("1. Vérifiez vos paramètres de connexion")
    print("2. Assurez-vous d'avoir les permissions SharePoint")
    print("3. Contactez votre administrateur IT si nécessaire")

print("\n🏁 Traitement terminé!")
