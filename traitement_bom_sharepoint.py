import os
from office365.sharepoint.client_context import ClientContext
from office365.runtime.auth.authentication_context import AuthenticationContext

# ---- 🔐 1. Paramètres à personnaliser ----
# 🔧 OPTION 1: URLs corrigées (format API SharePoint)
site_url = "https://uitacma.sharepoint.com/sites/YAZAKIInternship"  # 🛠️ URL du site (format API)
username = "<EMAIL>"
password = "Sppu2378"

# Chemin relatif du dossier où sont stockés les fichiers dans SharePoint
sharepoint_folder = "/sites/YAZAKIInternship/Shared Documents"  # 🛠️ chemin du dossier (format API)
test_file_name = "MasterBOM Test.xlsx"  # 🛠️ nom du fichier à tester

print("🔄 Début du test d'accès SharePoint...")

try:
    # ---- 🔐 2. Test d'authentification ----
    print("🔐 Étape 1: Test d'authentification...")
    ctx_auth = AuthenticationContext(site_url)

    if not ctx_auth.acquire_token_for_user(username, password):
        raise Exception("❌ Authentification échouée. Vérifie les identifiants et MFA.")

    print("✅ Authentification réussie!")

    # ---- 🌐 3. Test de connexion au site ----
    print("🌐 Étape 2: Test de connexion au site SharePoint...")
    ctx = ClientContext(site_url, ctx_auth)

    # Test basique de connexion
    web = ctx.web
    ctx.load(web)
    ctx.execute_query()

    print(f"✅ Connexion au site réussie! Titre du site: {web.title}")

    # ---- 📁 4. Test d'accès au dossier ----
    print("📁 Étape 3: Test d'accès au dossier...")
    try:
        folder = ctx.web.get_folder_by_server_relative_url(sharepoint_folder)
        ctx.load(folder)
        ctx.execute_query()
        print(f"✅ Accès au dossier réussi: {sharepoint_folder}")

        # ---- 📋 5. Lister les fichiers du dossier ----
        print("📋 Étape 4: Liste des fichiers dans le dossier...")
        files = folder.files
        ctx.load(files)
        ctx.execute_query()

        print(f"📁 Fichiers trouvés dans le dossier ({len(files)} fichiers):")
        for file in files:
            print(f"  - {file.name}")

        # ---- � 6. Test d'accès au fichier spécifique ----
        print(f"📄 Étape 5: Test d'accès au fichier '{test_file_name}'...")
        test_file_url = f"{sharepoint_folder}/{test_file_name}"

        try:
            file = ctx.web.get_file_by_server_relative_url(test_file_url)
            ctx.load(file)
            ctx.execute_query()

            print(f"✅ Fichier trouvé!")
            print(f"  - Nom: {file.name}")
            print(f"  - Taille: {file.length} bytes")
            print(f"  - URL: {file.server_relative_url}")

        except Exception as file_error:
            print(f"❌ Erreur d'accès au fichier '{test_file_name}': {file_error}")
            print("💡 Vérifiez que le nom du fichier est correct et qu'il existe dans le dossier.")

    except Exception as folder_error:
        print(f"❌ Erreur d'accès au dossier: {folder_error}")
        print("💡 Vérifiez le chemin du dossier SharePoint.")

except Exception as auth_error:
    print(f"❌ Erreur d'authentification: {auth_error}")
    print("💡 Vérifiez vos identifiants et l'URL du site.")

print("\n🏁 Test terminé!")
