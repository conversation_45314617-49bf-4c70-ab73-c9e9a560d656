import os
from office365.sharepoint.client_context import ClientContext
from office365.runtime.auth.user_credential import UserCredential

# ---- 🔐 OPTION 3: Authentification Interactive ----
# Cette méthode ouvre le navigateur pour l'authentification MFA

site_url = "https://uitacma.sharepoint.com/sites/YAZAKIInternship"
username = "<EMAIL>"

sharepoint_folder = "/sites/YAZAKIInternship/Shared Documents"
test_file_name = "MasterBOM Test.xlsx"

print("🔄 Test avec authentification interactive...")

try:
    # Authentification interactive (ouvre le navigateur)
    print("🔐 Authentification interactive (navigateur va s'ouvrir)...")
    
    # Méthode 1: UserCredential sans mot de passe (déclenche l'auth interactive)
    ctx = ClientContext(site_url).with_user_credentials(username, "")
    
    # Alternative: Authentification complètement interactive
    # ctx = ClientContext(site_url).with_interactive_authentication()
    
    # Test de connexion
    web = ctx.web
    ctx.load(web)
    ctx.execute_query()
    
    print(f"✅ Connexion réussie! Site: {web.title}")
    
    # Test d'accès au dossier
    folder = ctx.web.get_folder_by_server_relative_url(sharepoint_folder)
    ctx.load(folder)
    ctx.execute_query()
    print(f"✅ Accès au dossier réussi: {sharepoint_folder}")
    
    # Lister les fichiers
    files = folder.files
    ctx.load(files)
    ctx.execute_query()
    
    print(f"📁 Fichiers trouvés ({len(files)} fichiers):")
    for file in files:
        print(f"  - {file.name}")

except Exception as e:
    print(f"❌ Erreur: {e}")
    print("\n💡 Cette méthode nécessite:")
    print("- Un navigateur web")
    print("- Connexion interactive avec MFA")

print("\n🏁 Test terminé!")
